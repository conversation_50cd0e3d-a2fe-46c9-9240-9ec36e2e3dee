use anyhow::{Context, Result};
use chrono::{DateTime, Utc};
use opendal::Operator;
use std::collections::HashMap;
use tokio::sync::RwLock;

use crate::models::{Operation, ClipboardItem, Tombstone};
use crate::hlc::HLC;
use crate::storage_engine::StorageEngine;

/// 云端同步引擎
pub struct CloudSyncEngine {
    storage: StorageEngine,
    cloud_operator: Operator,
    user_id: String,
    device_id: String,
    state: RwLock<SyncState>,
}

#[derive(Debug, Default)]
struct SyncState {
    last_sync_hlc: Option<HLC>,
    device_cursors: HashMap<String, HLC>, // device_id -> last_synced_hlc
}

/// 快照数据结构（LWW-oplog架构）
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct Snapshot {
    /// 快照的 HLC 时间戳
    pub snapshot_hlc: HLC,
    /// 快照中的所有项目
    pub items: Vec<ClipboardItem>,
    /// 快照中的所有墓碑
    pub tombstones: Vec<Tombstone>,
    /// 创建快照的设备
    pub device_id: String,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

/// Manifest 文件结构（LWW-oplog架构）
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct Manifest {
    /// 最新快照的 HLC
    pub snapshot_hlc: Option<HLC>,
    /// 各设备的 oplog 游标
    pub oplog_cut: HashMap<String, HLC>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

impl CloudSyncEngine {
    pub fn new(
        storage: StorageEngine,
        cloud_operator: Operator,
        user_id: String,
        device_id: String,
    ) -> Self {
        Self {
            storage,
            cloud_operator,
            user_id,
            device_id,
            state: RwLock::new(SyncState::default()),
        }
    }

    /// 本地写入操作（立即上传到云端）
    pub async fn local_add(&self, item: ClipboardItem) -> Result<()> {
        // 写入本地存储
        let op = self.storage.add_item(item).await?;
        
        // 立即上传到云端
        self.upload_operation(&op).await?;
        
        Ok(())
    }

    /// 本地删除操作（立即上传到云端）
    pub async fn local_delete(&self, item_id: &str) -> Result<bool> {
        // 删除本地项目
        if let Some(op) = self.storage.delete_item(item_id).await? {
            // 立即上传到云端
            self.upload_operation(&op).await?;
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// 执行同步：拉取远端变更并应用到本地
    pub async fn sync(&self) -> Result<()> {
        tracing::debug!("开始云端同步...");

        // 获取已知的设备列表
        let known_devices = self.discover_devices().await?;
        
        // 对每个设备，增量拉取操作
        for device_id in &known_devices {
            if device_id == &self.device_id {
                continue; // 跳过自己
            }
            
            self.sync_device_operations(device_id).await?;
        }

        // 检查是否需要创建快照
        self.check_and_create_snapshot().await?;

        // 更新同步时间戳
        {
            let mut state = self.state.write().await;
            state.last_sync_hlc = Some(HLC::now());
        }

        tracing::debug!("云端同步完成");
        Ok(())
    }

    /// 同步特定设备的操作
    async fn sync_device_operations(&self, device_id: &str) -> Result<()> {
        let last_cursor = {
            let state = self.state.read().await;
            state.device_cursors.get(device_id).copied()
        };

        // 列举设备的 oplog
        let operations = self.fetch_device_operations(device_id, last_cursor).await?;
        
        if operations.is_empty() {
            return Ok(());
        }

        tracing::debug!("从设备 {} 获取到 {} 个操作", device_id, operations.len());

        // 应用操作到本地存储
        let mut latest_hlc = last_cursor;
        for op in operations {
            // 更新 HLC 生成器
            if let Ok(op_hlc) = op.hlc() {
                self.storage.update_hlc(op_hlc);
                latest_hlc = Some(op_hlc);
            }

            // 应用操作
            let result = self.storage.apply_operation(&op).await?;
            if result.applied {
                tracing::debug!("应用操作: {} -> {}", op.op_id, result.message);
            } else {
                tracing::debug!("拒绝操作: {} -> {}", op.op_id, result.message);
            }
        }

        // 更新设备游标
        if let Some(hlc) = latest_hlc {
            let mut state = self.state.write().await;
            state.device_cursors.insert(device_id.to_string(), hlc);
        }

        Ok(())
    }

    /// 上传单个操作到云端
    async fn upload_operation(&self, op: &Operation) -> Result<()> {
        let path = format!("{}/oplog/{}/{}.json", self.user_id, self.device_id, op.op_id);
        let content = serde_json::to_vec(op)?;

        self.cloud_operator
            .write(&path, content)
            .await
            .context("上传操作失败")?;

        tracing::debug!("上传操作到云端: {}", path);
        Ok(())
    }

    /// 发现已知设备（通过扫描oplog目录）
    async fn discover_devices(&self) -> Result<Vec<String>> {
        let prefix = format!("{}/oplog/", self.user_id);

        let mut devices = std::collections::HashSet::new();

        // 使用 OpenDAL 的 list 方法
        let entries = self.cloud_operator
            .list(&prefix)
            .await
            .context("列举设备目录失败")?;

        for entry in entries {
            let path = entry.path();
            if let Some(relative_path) = path.strip_prefix(&prefix) {
                if let Some(device_id) = relative_path.split('/').next() {
                    if !device_id.is_empty() {
                        devices.insert(device_id.to_string());
                    }
                }
            }
        }

        Ok(devices.into_iter().collect())
    }

    /// 获取设备的操作列表（增量拉取）
    async fn fetch_device_operations(&self, device_id: &str, since_hlc: Option<HLC>) -> Result<Vec<Operation>> {
        let prefix = format!("{}/oplog/{}/", self.user_id, device_id);

        // 获取所有操作文件
        let entries = self.cloud_operator
            .list(&prefix)
            .await
            .context("列举操作文件失败")?;

        let mut operations = Vec::new();
        let since_str = since_hlc.map(|hlc| hlc.to_string()).unwrap_or_else(|| "0".to_string());

        for entry in entries {
            let path = entry.path();

            // 从文件名提取 HLC
            if let Some(filename) = path.strip_prefix(&prefix) {
                if let Some(hlc_str) = filename.strip_suffix(".json") {
                    // 只处理比 since_hlc 更新的操作
                    if hlc_str > since_str.as_str() {
                        // 读取操作文件
                        match self.cloud_operator.read(path).await {
                            Ok(content) => {
                                let bytes = content.to_vec();
                                match serde_json::from_slice::<Operation>(&bytes) {
                                    Ok(op) => operations.push(op),
                                    Err(e) => {
                                        tracing::warn!("解析操作文件失败 {}: {}", path, e);
                                    }
                                }
                            }
                            Err(e) => {
                                tracing::warn!("读取操作文件失败 {}: {}", path, e);
                            }
                        }
                    }
                }
            }
        }

        // 按 HLC 排序
        operations.sort_by(|a, b| {
            match (a.hlc(), b.hlc()) {
                (Ok(a_hlc), Ok(b_hlc)) => a_hlc.cmp(&b_hlc),
                _ => std::cmp::Ordering::Equal,
            }
        });

        Ok(operations)
    }

    /// 检查是否需要创建快照
    async fn check_and_create_snapshot(&self) -> Result<()> {
        let stats = self.storage.get_stats().await?;
        
        // 快照触发条件
        let should_snapshot = stats.pending_ops >= 5000 
            || stats.database_size >= 5 * 1024 * 1024 // 5 MiB
            || self.should_snapshot_by_time().await?;

        if should_snapshot {
            self.create_snapshot().await?;
        }

        Ok(())
    }

    /// 基于时间判断是否需要快照
    async fn should_snapshot_by_time(&self) -> Result<bool> {
        // 获取最新快照时间
        if let Some(latest_manifest) = self.get_latest_manifest().await? {
            let elapsed = Utc::now().signed_duration_since(latest_manifest.created_at);
            Ok(elapsed.num_minutes() >= 30) // 30 分钟
        } else {
            Ok(true) // 没有快照，需要创建
        }
    }

    /// 创建快照
    pub async fn create_snapshot(&self) -> Result<()> {
        tracing::info!("开始创建快照...");

        let hlc = HLC::now();
        let items = self.storage.get_all_items().await?;
        
        // 获取墓碑数据
        let tombstones = self.storage.get_all_tombstones().await?;

        // 创建快照
        let snapshot = Snapshot {
            snapshot_hlc: hlc,
            items,
            tombstones,
            device_id: self.device_id.clone(),
            created_at: Utc::now(),
        };

        // 压缩并上传快照
        let snapshot_data = serde_json::to_vec(&snapshot)?;
        let compressed = self.compress_data(&snapshot_data)?;
        
        let snapshot_path = format!("{}/snapshot/{}.lz4", self.user_id, hlc.to_string());
        self.cloud_operator
            .write(&snapshot_path, compressed)
            .await?;

        // 更新 manifest
        let device_cursors = {
            let state = self.state.read().await;
            state.device_cursors.clone()
        };

        let manifest = Manifest {
            snapshot_hlc: Some(hlc),
            oplog_cut: device_cursors,
            created_at: Utc::now(),
        };

        let manifest_data = serde_json::to_vec(&manifest)?;
        let manifest_path = format!("{}/manifest.json", self.user_id);
        self.cloud_operator
            .write(&manifest_path, manifest_data)
            .await?;

        tracing::info!("快照创建完成: {}", snapshot_path);
        Ok(())
    }

    /// 获取最新的 manifest
    async fn get_latest_manifest(&self) -> Result<Option<Manifest>> {
        let manifest_path = format!("{}/manifest.json", self.user_id);
        
        match self.cloud_operator.read(&manifest_path).await {
            Ok(data) => {
                let bytes = data.to_vec();
                let manifest: Manifest = serde_json::from_slice(&bytes)?;
                Ok(Some(manifest))
            }
            Err(_) => Ok(None), // manifest 不存在
        }
    }

    /// 从快照恢复（冷启动）
    pub async fn restore_from_snapshot(&self) -> Result<()> {
        tracing::info!("开始从快照恢复...");

        // 获取最新 manifest
        let manifest = match self.get_latest_manifest().await? {
            Some(m) => m,
            None => {
                tracing::info!("没有找到快照，跳过恢复");
                return Ok(());
            }
        };

        // 下载并恢复快照
        if let Some(snapshot_hlc) = manifest.snapshot_hlc {
            let snapshot_path = format!("{}/snapshot/{}.lz4", self.user_id, snapshot_hlc.to_string());
            
            match self.cloud_operator.read(&snapshot_path).await {
                Ok(compressed_data) => {
                    let data = self.decompress_data(&compressed_data.to_vec())?;
                    let snapshot: Snapshot = serde_json::from_slice(&data)?;
                    
                    // 清空本地数据
                    self.storage.clear_all().await?;
                    
                    // 恢复快照数据
                    for item in snapshot.items {
                        // 直接插入，跳过 HLC 生成
                        if let Err(e) = self.storage.apply_operation(&Operation::new_add(
                            item,
                            snapshot_hlc,
                            snapshot.device_id.clone(),
                        )).await {
                            tracing::warn!("恢复项目失败: {}", e);
                        }
                    }

                    tracing::info!("快照恢复完成");
                }
                Err(e) => {
                    tracing::warn!("下载快照失败: {}", e);
                }
            }
        }

        // 根据 oplog_cut 增量恢复
        {
            let mut state = self.state.write().await;
            state.device_cursors = manifest.oplog_cut;
        }

        // 执行增量同步
        self.sync().await?;

        Ok(())
    }

    /// 压缩数据
    fn compress_data(&self, data: &[u8]) -> Result<Vec<u8>> {
        use lz4_flex::compress_prepend_size;
        Ok(compress_prepend_size(data))
    }

    /// 解压数据
    fn decompress_data(&self, compressed: &[u8]) -> Result<Vec<u8>> {
        use lz4_flex::decompress_size_prepended;
        Ok(decompress_size_prepended(compressed)?)
    }

    /// 获取同步状态
    pub async fn get_status(&self) -> Result<serde_json::Value> {
        let state = self.state.read().await;
        let stats = self.storage.get_stats().await?;

        Ok(serde_json::json!({
            "last_sync_hlc": state.last_sync_hlc,
            "device_cursors": state.device_cursors,
            "storage_stats": stats,
            "user_id": self.user_id,
            "device_id": self.device_id,
        }))
    }

    /// 清空所有数据（本地+云端）
    pub async fn clear_all(&self) -> Result<()> {
        // 清空本地数据
        self.storage.clear_all().await?;

        // 清空云端数据（通过删除操作）
        let all_items = self.storage.get_all_items().await?;
        for item in all_items {
            self.local_delete(&item.item_id).await?;
        }

        Ok(())
    }
} 