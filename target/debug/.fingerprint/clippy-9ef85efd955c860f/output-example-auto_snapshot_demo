{"$message_type":"diagnostic","message":"unresolved import `clippy_lib::hybrid_storage`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src-tauri/examples/auto_snapshot_demo.rs","byte_start":36,"byte_end":50,"line_start":2,"line_end":2,"column_start":17,"column_end":31,"is_primary":true,"text":[{"text":"use clippy_lib::hybrid_storage::HybridStorageEngine;","highlight_start":17,"highlight_end":31}],"label":"could not find `hybrid_storage` in `clippy_lib`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m: unresolved import `clippy_lib::hybrid_storage`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc-tauri/examples/auto_snapshot_demo.rs:2:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse clippy_lib::hybrid_storage::HybridStorageEngine;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `hybrid_storage` in `clippy_lib`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `clippy_lib::hybrid_sync`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src-tauri/examples/auto_snapshot_demo.rs","byte_start":89,"byte_end":100,"line_start":3,"line_end":3,"column_start":17,"column_end":28,"is_primary":true,"text":[{"text":"use clippy_lib::hybrid_sync::HybridSyncEngine;","highlight_start":17,"highlight_end":28}],"label":"could not find `hybrid_sync` in `clippy_lib`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m: unresolved import `clippy_lib::hybrid_sync`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc-tauri/examples/auto_snapshot_demo.rs:3:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse clippy_lib::hybrid_sync::HybridSyncEngine;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `hybrid_sync` in `clippy_lib`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `clippy_lib::sync`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src-tauri/examples/auto_snapshot_demo.rs","byte_start":136,"byte_end":140,"line_start":4,"line_end":4,"column_start":17,"column_end":21,"is_primary":true,"text":[{"text":"use clippy_lib::sync::{SyncEngine, SyncConfig};","highlight_start":17,"highlight_end":21}],"label":"could not find `sync` in `clippy_lib`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m: unresolved import `clippy_lib::sync`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc-tauri/examples/auto_snapshot_demo.rs:4:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse clippy_lib::sync::{SyncEngine, SyncConfig};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `sync` in `clippy_lib`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `clippy_lib::storage`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src-tauri/examples/auto_snapshot_demo.rs","byte_start":184,"byte_end":191,"line_start":5,"line_end":5,"column_start":17,"column_end":24,"is_primary":true,"text":[{"text":"use clippy_lib::storage::ClipboardItem;","highlight_start":17,"highlight_end":24}],"label":"could not find `storage` in `clippy_lib`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m: unresolved import `clippy_lib::storage`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc-tauri/examples/auto_snapshot_demo.rs:5:17\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse clippy_lib::storage::ClipboardItem;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `storage` in `clippy_lib`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type annotations needed for `Arc<_>`","code":{"code":"E0282","explanation":"The compiler could not infer a type and asked for a type annotation.\n\nErroneous code example:\n\n```compile_fail,E0282\nlet x = Vec::new();\n```\n\nThis error indicates that type inference did not result in one unique possible\ntype, and extra information is required. In most cases this can be provided\nby adding a type annotation. Sometimes you need to specify a generic type\nparameter manually.\n\nIn the example above, type `Vec` has a type parameter `T`. When calling\n`Vec::new`, barring any other later usage of the variable `x` that allows the\ncompiler to infer what type `T` is, the compiler needs to be told what it is.\n\nThe type can be specified on the variable:\n\n```\nlet x: Vec<i32> = Vec::new();\n```\n\nThe type can also be specified in the path of the expression:\n\n```\nlet x = Vec::<i32>::new();\n```\n\nIn cases with more complex types, it is not necessary to annotate the full\ntype. Once the ambiguity is resolved, the compiler can infer the rest:\n\n```\nlet x: Vec<_> = \"hello\".chars().rev().collect();\n```\n\nAnother way to provide the compiler with enough information, is to specify the\ngeneric type parameter:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<char>>();\n```\n\nAgain, you need not specify the full type if the compiler can infer it:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<_>>();\n```\n\nApart from a method or function with a generic type parameter, this error can\noccur when a type parameter of a struct or trait cannot be inferred. In that\ncase it is not always possible to use a type annotation, because all candidates\nhave the same return type. For instance:\n\n```compile_fail,E0282\nstruct Foo<T> {\n    num: T,\n}\n\nimpl<T> Foo<T> {\n    fn bar() -> i32 {\n        0\n    }\n\n    fn baz() {\n        let number = Foo::bar();\n    }\n}\n```\n\nThis will fail because the compiler does not know which instance of `Foo` to\ncall `bar` on. Change `Foo::bar()` to `Foo::<T>::bar()` to resolve the error.\n"},"level":"error","spans":[{"file_name":"src-tauri/examples/auto_snapshot_demo.rs","byte_start":1909,"byte_end":1914,"line_start":54,"line_end":54,"column_start":25,"column_end":30,"is_primary":false,"text":[{"text":"        hybrid_storage1.clone(),","highlight_start":25,"highlight_end":30}],"label":"type must be known at this point","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src-tauri/examples/auto_snapshot_demo.rs","byte_start":1435,"byte_end":1450,"line_start":42,"line_end":42,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    let hybrid_storage1 = Arc::new(","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider giving `hybrid_storage1` an explicit type, where the type for type parameter `T` is specified","code":null,"level":"help","spans":[{"file_name":"src-tauri/examples/auto_snapshot_demo.rs","byte_start":1450,"byte_end":1450,"line_start":42,"line_end":42,"column_start":24,"column_end":24,"is_primary":true,"text":[{"text":"    let hybrid_storage1 = Arc::new(","highlight_start":24,"highlight_end":24}],"label":null,"suggested_replacement":": Arc<T>","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0282]\u001b[0m\u001b[0m\u001b[1m: type annotations needed for `Arc<_>`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc-tauri/examples/auto_snapshot_demo.rs:42:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let hybrid_storage1 = Arc::new(\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        hybrid_storage1.clone(),\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mtype must be known at this point\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider giving `hybrid_storage1` an explicit type, where the type for type parameter `T` is specified\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    let hybrid_storage1\u001b[0m\u001b[0m\u001b[38;5;10m: Arc<T>\u001b[0m\u001b[0m = Arc::new(\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[38;5;10m++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type annotations needed for `Arc<_>`","code":{"code":"E0282","explanation":"The compiler could not infer a type and asked for a type annotation.\n\nErroneous code example:\n\n```compile_fail,E0282\nlet x = Vec::new();\n```\n\nThis error indicates that type inference did not result in one unique possible\ntype, and extra information is required. In most cases this can be provided\nby adding a type annotation. Sometimes you need to specify a generic type\nparameter manually.\n\nIn the example above, type `Vec` has a type parameter `T`. When calling\n`Vec::new`, barring any other later usage of the variable `x` that allows the\ncompiler to infer what type `T` is, the compiler needs to be told what it is.\n\nThe type can be specified on the variable:\n\n```\nlet x: Vec<i32> = Vec::new();\n```\n\nThe type can also be specified in the path of the expression:\n\n```\nlet x = Vec::<i32>::new();\n```\n\nIn cases with more complex types, it is not necessary to annotate the full\ntype. Once the ambiguity is resolved, the compiler can infer the rest:\n\n```\nlet x: Vec<_> = \"hello\".chars().rev().collect();\n```\n\nAnother way to provide the compiler with enough information, is to specify the\ngeneric type parameter:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<char>>();\n```\n\nAgain, you need not specify the full type if the compiler can infer it:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<_>>();\n```\n\nApart from a method or function with a generic type parameter, this error can\noccur when a type parameter of a struct or trait cannot be inferred. In that\ncase it is not always possible to use a type annotation, because all candidates\nhave the same return type. For instance:\n\n```compile_fail,E0282\nstruct Foo<T> {\n    num: T,\n}\n\nimpl<T> Foo<T> {\n    fn bar() -> i32 {\n        0\n    }\n\n    fn baz() {\n        let number = Foo::bar();\n    }\n}\n```\n\nThis will fail because the compiler does not know which instance of `Foo` to\ncall `bar` on. Change `Foo::bar()` to `Foo::<T>::bar()` to resolve the error.\n"},"level":"error","spans":[{"file_name":"src-tauri/examples/auto_snapshot_demo.rs","byte_start":3905,"byte_end":3910,"line_start":109,"line_end":109,"column_start":25,"column_end":30,"is_primary":false,"text":[{"text":"        hybrid_storage2.clone(),","highlight_start":25,"highlight_end":30}],"label":"type must be known at this point","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src-tauri/examples/auto_snapshot_demo.rs","byte_start":3452,"byte_end":3467,"line_start":97,"line_end":97,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"    let hybrid_storage2 = Arc::new(","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consider giving `hybrid_storage2` an explicit type, where the type for type parameter `T` is specified","code":null,"level":"help","spans":[{"file_name":"src-tauri/examples/auto_snapshot_demo.rs","byte_start":3467,"byte_end":3467,"line_start":97,"line_end":97,"column_start":24,"column_end":24,"is_primary":true,"text":[{"text":"    let hybrid_storage2 = Arc::new(","highlight_start":24,"highlight_end":24}],"label":null,"suggested_replacement":": Arc<T>","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0282]\u001b[0m\u001b[0m\u001b[1m: type annotations needed for `Arc<_>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc-tauri/examples/auto_snapshot_demo.rs:97:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m97\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let hybrid_storage2 = Arc::new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m109\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        hybrid_storage2.clone(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mtype must be known at this point\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider giving `hybrid_storage2` an explicit type, where the type for type parameter `T` is specified\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m97\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    let hybrid_storage2\u001b[0m\u001b[0m\u001b[38;5;10m: Arc<T>\u001b[0m\u001b[0m = Arc::new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[38;5;10m++++++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type annotations needed","code":{"code":"E0282","explanation":"The compiler could not infer a type and asked for a type annotation.\n\nErroneous code example:\n\n```compile_fail,E0282\nlet x = Vec::new();\n```\n\nThis error indicates that type inference did not result in one unique possible\ntype, and extra information is required. In most cases this can be provided\nby adding a type annotation. Sometimes you need to specify a generic type\nparameter manually.\n\nIn the example above, type `Vec` has a type parameter `T`. When calling\n`Vec::new`, barring any other later usage of the variable `x` that allows the\ncompiler to infer what type `T` is, the compiler needs to be told what it is.\n\nThe type can be specified on the variable:\n\n```\nlet x: Vec<i32> = Vec::new();\n```\n\nThe type can also be specified in the path of the expression:\n\n```\nlet x = Vec::<i32>::new();\n```\n\nIn cases with more complex types, it is not necessary to annotate the full\ntype. Once the ambiguity is resolved, the compiler can infer the rest:\n\n```\nlet x: Vec<_> = \"hello\".chars().rev().collect();\n```\n\nAnother way to provide the compiler with enough information, is to specify the\ngeneric type parameter:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<char>>();\n```\n\nAgain, you need not specify the full type if the compiler can infer it:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<_>>();\n```\n\nApart from a method or function with a generic type parameter, this error can\noccur when a type parameter of a struct or trait cannot be inferred. In that\ncase it is not always possible to use a type annotation, because all candidates\nhave the same return type. For instance:\n\n```compile_fail,E0282\nstruct Foo<T> {\n    num: T,\n}\n\nimpl<T> Foo<T> {\n    fn bar() -> i32 {\n        0\n    }\n\n    fn baz() {\n        let number = Foo::bar();\n    }\n}\n```\n\nThis will fail because the compiler does not know which instance of `Foo` to\ncall `bar` on. Change `Foo::bar()` to `Foo::<T>::bar()` to resolve the error.\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":6795,"byte_end":6801,"line_start":184,"line_end":184,"column_start":28,"column_end":34,"is_primary":true,"text":[{"text":"                    if fut.as_mut().poll(cx).is_pending() {","highlight_start":28,"highlight_end":34}],"label":"cannot infer type","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":8095,"byte_end":8169,"line_start":220,"line_end":220,"column_start":7,"column_end":81,"is_primary":false,"text":[{"text":"      $crate::try_join!(@{ ($($s)* _) ($($n)* + 1) $($t)* ($($s)*) $e, } $($r)*)","highlight_start":7,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":8095,"byte_end":8169,"line_start":220,"line_end":220,"column_start":7,"column_end":81,"is_primary":false,"text":[{"text":"      $crate::try_join!(@{ ($($s)* _) ($($n)* + 1) $($t)* ($($s)*) $e, } $($r)*)","highlight_start":7,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":8249,"byte_end":8287,"line_start":226,"line_end":226,"column_start":9,"column_end":47,"is_primary":false,"text":[{"text":"        $crate::try_join!(@{ () (0) } $($e,)*)","highlight_start":9,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src-tauri/examples/auto_snapshot_demo.rs","byte_start":7074,"byte_end":7122,"line_start":194,"line_end":194,"column_start":30,"column_end":78,"is_primary":false,"text":[{"text":"    let (result1, result2) = tokio::try_join!(snapshot1_task, snapshot2_task)?;","highlight_start":30,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"tokio::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0282]\u001b[0m\u001b[0m\u001b[1m: type annotations needed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc-tauri/examples/auto_snapshot_demo.rs:194:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let (result1, result2) = tokio::try_join!(snapshot1_task, snapshot2_task)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcannot infer type\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::try_join` which comes from the expansion of the macro `tokio::try_join` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type annotations needed","code":{"code":"E0282","explanation":"The compiler could not infer a type and asked for a type annotation.\n\nErroneous code example:\n\n```compile_fail,E0282\nlet x = Vec::new();\n```\n\nThis error indicates that type inference did not result in one unique possible\ntype, and extra information is required. In most cases this can be provided\nby adding a type annotation. Sometimes you need to specify a generic type\nparameter manually.\n\nIn the example above, type `Vec` has a type parameter `T`. When calling\n`Vec::new`, barring any other later usage of the variable `x` that allows the\ncompiler to infer what type `T` is, the compiler needs to be told what it is.\n\nThe type can be specified on the variable:\n\n```\nlet x: Vec<i32> = Vec::new();\n```\n\nThe type can also be specified in the path of the expression:\n\n```\nlet x = Vec::<i32>::new();\n```\n\nIn cases with more complex types, it is not necessary to annotate the full\ntype. Once the ambiguity is resolved, the compiler can infer the rest:\n\n```\nlet x: Vec<_> = \"hello\".chars().rev().collect();\n```\n\nAnother way to provide the compiler with enough information, is to specify the\ngeneric type parameter:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<char>>();\n```\n\nAgain, you need not specify the full type if the compiler can infer it:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<_>>();\n```\n\nApart from a method or function with a generic type parameter, this error can\noccur when a type parameter of a struct or trait cannot be inferred. In that\ncase it is not always possible to use a type annotation, because all candidates\nhave the same return type. For instance:\n\n```compile_fail,E0282\nstruct Foo<T> {\n    num: T,\n}\n\nimpl<T> Foo<T> {\n    fn bar() -> i32 {\n        0\n    }\n\n    fn baz() {\n        let number = Foo::bar();\n    }\n}\n```\n\nThis will fail because the compiler does not know which instance of `Foo` to\ncall `bar` on. Change `Foo::bar()` to `Foo::<T>::bar()` to resolve the error.\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":6905,"byte_end":6911,"line_start":186,"line_end":186,"column_start":35,"column_end":41,"is_primary":true,"text":[{"text":"                    } else if fut.as_mut().output_mut().expect(\"expected completed future\").is_err() {","highlight_start":35,"highlight_end":41}],"label":"cannot infer type","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":8095,"byte_end":8169,"line_start":220,"line_end":220,"column_start":7,"column_end":81,"is_primary":false,"text":[{"text":"      $crate::try_join!(@{ ($($s)* _) ($($n)* + 1) $($t)* ($($s)*) $e, } $($r)*)","highlight_start":7,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":8095,"byte_end":8169,"line_start":220,"line_end":220,"column_start":7,"column_end":81,"is_primary":false,"text":[{"text":"      $crate::try_join!(@{ ($($s)* _) ($($n)* + 1) $($t)* ($($s)*) $e, } $($r)*)","highlight_start":7,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":8249,"byte_end":8287,"line_start":226,"line_end":226,"column_start":9,"column_end":47,"is_primary":false,"text":[{"text":"        $crate::try_join!(@{ () (0) } $($e,)*)","highlight_start":9,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src-tauri/examples/auto_snapshot_demo.rs","byte_start":7074,"byte_end":7122,"line_start":194,"line_end":194,"column_start":30,"column_end":78,"is_primary":false,"text":[{"text":"    let (result1, result2) = tokio::try_join!(snapshot1_task, snapshot2_task)?;","highlight_start":30,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"tokio::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0282]\u001b[0m\u001b[0m\u001b[1m: type annotations needed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc-tauri/examples/auto_snapshot_demo.rs:194:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let (result1, result2) = tokio::try_join!(snapshot1_task, snapshot2_task)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcannot infer type\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::try_join` which comes from the expansion of the macro `tokio::try_join` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type annotations needed","code":{"code":"E0282","explanation":"The compiler could not infer a type and asked for a type annotation.\n\nErroneous code example:\n\n```compile_fail,E0282\nlet x = Vec::new();\n```\n\nThis error indicates that type inference did not result in one unique possible\ntype, and extra information is required. In most cases this can be provided\nby adding a type annotation. Sometimes you need to specify a generic type\nparameter manually.\n\nIn the example above, type `Vec` has a type parameter `T`. When calling\n`Vec::new`, barring any other later usage of the variable `x` that allows the\ncompiler to infer what type `T` is, the compiler needs to be told what it is.\n\nThe type can be specified on the variable:\n\n```\nlet x: Vec<i32> = Vec::new();\n```\n\nThe type can also be specified in the path of the expression:\n\n```\nlet x = Vec::<i32>::new();\n```\n\nIn cases with more complex types, it is not necessary to annotate the full\ntype. Once the ambiguity is resolved, the compiler can infer the rest:\n\n```\nlet x: Vec<_> = \"hello\".chars().rev().collect();\n```\n\nAnother way to provide the compiler with enough information, is to specify the\ngeneric type parameter:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<char>>();\n```\n\nAgain, you need not specify the full type if the compiler can infer it:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<_>>();\n```\n\nApart from a method or function with a generic type parameter, this error can\noccur when a type parameter of a struct or trait cannot be inferred. In that\ncase it is not always possible to use a type annotation, because all candidates\nhave the same return type. For instance:\n\n```compile_fail,E0282\nstruct Foo<T> {\n    num: T,\n}\n\nimpl<T> Foo<T> {\n    fn bar() -> i32 {\n        0\n    }\n\n    fn baz() {\n        let number = Foo::bar();\n    }\n}\n```\n\nThis will fail because the compiler does not know which instance of `Foo` to\ncall `bar` on. Change `Foo::bar()` to `Foo::<T>::bar()` to resolve the error.\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":7019,"byte_end":7030,"line_start":187,"line_end":187,"column_start":46,"column_end":57,"is_primary":true,"text":[{"text":"                        return Ready(Err(fut.take_output().expect(\"expected completed future\").err().unwrap()))","highlight_start":46,"highlight_end":57}],"label":"cannot infer type","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":8095,"byte_end":8169,"line_start":220,"line_end":220,"column_start":7,"column_end":81,"is_primary":false,"text":[{"text":"      $crate::try_join!(@{ ($($s)* _) ($($n)* + 1) $($t)* ($($s)*) $e, } $($r)*)","highlight_start":7,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":8095,"byte_end":8169,"line_start":220,"line_end":220,"column_start":7,"column_end":81,"is_primary":false,"text":[{"text":"      $crate::try_join!(@{ ($($s)* _) ($($n)* + 1) $($t)* ($($s)*) $e, } $($r)*)","highlight_start":7,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":8249,"byte_end":8287,"line_start":226,"line_end":226,"column_start":9,"column_end":47,"is_primary":false,"text":[{"text":"        $crate::try_join!(@{ () (0) } $($e,)*)","highlight_start":9,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src-tauri/examples/auto_snapshot_demo.rs","byte_start":7074,"byte_end":7122,"line_start":194,"line_end":194,"column_start":30,"column_end":78,"is_primary":false,"text":[{"text":"    let (result1, result2) = tokio::try_join!(snapshot1_task, snapshot2_task)?;","highlight_start":30,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"tokio::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0282]\u001b[0m\u001b[0m\u001b[1m: type annotations needed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc-tauri/examples/auto_snapshot_demo.rs:194:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let (result1, result2) = tokio::try_join!(snapshot1_task, snapshot2_task)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcannot infer type\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::try_join` which comes from the expansion of the macro `tokio::try_join` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type annotations needed","code":{"code":"E0282","explanation":"The compiler could not infer a type and asked for a type annotation.\n\nErroneous code example:\n\n```compile_fail,E0282\nlet x = Vec::new();\n```\n\nThis error indicates that type inference did not result in one unique possible\ntype, and extra information is required. In most cases this can be provided\nby adding a type annotation. Sometimes you need to specify a generic type\nparameter manually.\n\nIn the example above, type `Vec` has a type parameter `T`. When calling\n`Vec::new`, barring any other later usage of the variable `x` that allows the\ncompiler to infer what type `T` is, the compiler needs to be told what it is.\n\nThe type can be specified on the variable:\n\n```\nlet x: Vec<i32> = Vec::new();\n```\n\nThe type can also be specified in the path of the expression:\n\n```\nlet x = Vec::<i32>::new();\n```\n\nIn cases with more complex types, it is not necessary to annotate the full\ntype. Once the ambiguity is resolved, the compiler can infer the rest:\n\n```\nlet x: Vec<_> = \"hello\".chars().rev().collect();\n```\n\nAnother way to provide the compiler with enough information, is to specify the\ngeneric type parameter:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<char>>();\n```\n\nAgain, you need not specify the full type if the compiler can infer it:\n\n```\nlet x = \"hello\".chars().rev().collect::<Vec<_>>();\n```\n\nApart from a method or function with a generic type parameter, this error can\noccur when a type parameter of a struct or trait cannot be inferred. In that\ncase it is not always possible to use a type annotation, because all candidates\nhave the same return type. For instance:\n\n```compile_fail,E0282\nstruct Foo<T> {\n    num: T,\n}\n\nimpl<T> Foo<T> {\n    fn bar() -> i32 {\n        0\n    }\n\n    fn baz() {\n        let number = Foo::bar();\n    }\n}\n```\n\nThis will fail because the compiler does not know which instance of `Foo` to\ncall `bar` on. Change `Foo::bar()` to `Foo::<T>::bar()` to resolve the error.\n"},"level":"error","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":7765,"byte_end":7776,"line_start":208,"line_end":208,"column_start":26,"column_end":37,"is_primary":true,"text":[{"text":"                        .take_output()","highlight_start":26,"highlight_end":37}],"label":"cannot infer type","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":8095,"byte_end":8169,"line_start":220,"line_end":220,"column_start":7,"column_end":81,"is_primary":false,"text":[{"text":"      $crate::try_join!(@{ ($($s)* _) ($($n)* + 1) $($t)* ($($s)*) $e, } $($r)*)","highlight_start":7,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":8095,"byte_end":8169,"line_start":220,"line_end":220,"column_start":7,"column_end":81,"is_primary":false,"text":[{"text":"      $crate::try_join!(@{ ($($s)* _) ($($n)* + 1) $($t)* ($($s)*) $e, } $($r)*)","highlight_start":7,"highlight_end":81}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":8249,"byte_end":8287,"line_start":226,"line_end":226,"column_start":9,"column_end":47,"is_primary":false,"text":[{"text":"        $crate::try_join!(@{ () (0) } $($e,)*)","highlight_start":9,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src-tauri/examples/auto_snapshot_demo.rs","byte_start":7074,"byte_end":7122,"line_start":194,"line_end":194,"column_start":30,"column_end":78,"is_primary":false,"text":[{"text":"    let (result1, result2) = tokio::try_join!(snapshot1_task, snapshot2_task)?;","highlight_start":30,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"tokio::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::try_join!","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tokio-1.45.1/src/macros/try_join.rs","byte_start":4043,"byte_end":4064,"line_start":115,"line_end":115,"column_start":7,"column_end":28,"is_primary":false,"text":[{"text":"doc! {macro_rules! try_join {","highlight_start":7,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0282]\u001b[0m\u001b[0m\u001b[1m: type annotations needed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc-tauri/examples/auto_snapshot_demo.rs:194:30\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m194\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let (result1, result2) = tokio::try_join!(snapshot1_task, snapshot2_task)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcannot infer type\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::try_join` which comes from the expansion of the macro `tokio::try_join` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 10 previous errors","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 10 previous errors\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0282, E0432.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0282, E0432.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0282`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0282`.\u001b[0m\n"}
